
<script setup>
const url = "https://ttt3.qiuqiu-test.com/api/demo/test3"
const url1 = "https://ttt3.qiuqiu-test.com/api/demo/test3"


const fetchUrl = async (url) => {
  const res = await fetch(url,{
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'ops': 'e06e87cf-a37a-4211-bb2b-3b4bd1d3cae9',
      'ot': 'b2318859-380a-4c6c-8fcf-f9eda4c29345',
      'username': 'otpx3335680805'
    },
  })
  console.log(res)
}
</script>
<template>
  <div>
    <div @click="fetchUrl(url)">click me1</div>
    <div @click="fetchUrl(url1)">click me2</div>
    <NuxtRouteAnnouncer />
    <NuxtWelcome />
  </div>
</template>
